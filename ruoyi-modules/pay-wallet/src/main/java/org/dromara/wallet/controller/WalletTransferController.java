package org.dromara.wallet.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.stp.StpUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.R;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.web.core.BaseController;
import org.dromara.wallet.config.ChainType;
import org.dromara.wallet.config.facade.*;
import org.dromara.wallet.domain.dto.WalletWithdrawBo;
import org.dromara.wallet.domain.vo.CapitalWalletVo;
import org.dromara.wallet.utils.WalletPrivateKeyUtil;
import org.dromara.wallet.wallet.helper.EvmHelper;
import org.dromara.wallet.wallet.helper.TronHelper;
import org.dromara.wallet.wallet.transfer.dto.TransferRequest;
import org.dromara.wallet.wallet.transfer.dto.UnifiedTransferResult;
import org.dromara.wallet.wallet.transfer.service.UnifiedTransferService;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 区块链通用控制器
 *
 * <AUTHOR>
 * @date 2025/1/27
 */
@Slf4j
@RestController
@RequestMapping("/transfer")
@RequiredArgsConstructor
public class WalletTransferController extends BaseController {


    private final SolanaConfigFacade solanaConfigFacade;

    private final BscConfigFacade bscConfigFacade;
    private final ArbConfigFacade arbConfigFacade;
    private final BaseConfigFacade baseConfigFacade;
    private final TronConfigFacade tronConfigFacade;

    private final UnifiedTransferService unifiedTransferService;
    private final TronHelper tronHelper;
    private final EvmHelper evmHelper;


    /**
     * 获取资金钱包余额信息
     */
    @SaCheckPermission("wallet:transfer:query")
    @GetMapping("/getCapitalWallet")
    public R<CapitalWalletVo> getCapitalWallet(ChainType chain) {
        CapitalWalletVo capitalWalletVo = new CapitalWalletVo();
        if (chain == ChainType.TRON) {
            String fundingWalletAddress = tronConfigFacade.getFeeWalletAddress();
            capitalWalletVo.setWalletAddress(fundingWalletAddress);
            BigDecimal trxBalance = tronHelper.balanceGetNativeForRead(fundingWalletAddress);
            capitalWalletVo.getCoins().add(new CapitalWalletVo.CoinVo("TRX", trxBalance));
            for (String enabledTokenSymbol : tronConfigFacade.getEnabledTokenSymbols()) {
                BigDecimal tokenBalance = tronHelper.balanceGetForRead(fundingWalletAddress, enabledTokenSymbol);
                capitalWalletVo.getCoins().add(new CapitalWalletVo.CoinVo(enabledTokenSymbol, tokenBalance));
            }
        } else if (chain == ChainType.BSC) {
            String fundingWalletAddress = bscConfigFacade.getFeeWalletAddress();
            capitalWalletVo.setWalletAddress(fundingWalletAddress);
            BigDecimal trxBalance = evmHelper.balanceGetForRead(fundingWalletAddress, "BNB", bscConfigFacade);
            capitalWalletVo.getCoins().add(new CapitalWalletVo.CoinVo("BNB", trxBalance));
            for (String enabledTokenSymbol : bscConfigFacade.getEnabledTokenSymbols()) {
                BigDecimal tokenBalance = evmHelper.balanceGetForRead(fundingWalletAddress, enabledTokenSymbol, bscConfigFacade);
                capitalWalletVo.getCoins().add(new CapitalWalletVo.CoinVo(enabledTokenSymbol, tokenBalance));
            }
        }
        return R.ok(capitalWalletVo);
    }


    /**
     * 提款：一对多转账功能（新实现）
     * @param walletWithdrawBoList 提款请求参数列表
     */
    @SaCheckPermission("wallet:transfer:withdraw")
    @Log(title = "区块链钱包提款", businessType = BusinessType.WITHDRAW)
    @PostMapping("/withdraw")
    public R<String> withdrawNew(@RequestBody List<WalletWithdrawBo> walletWithdrawBoList) {
        if (walletWithdrawBoList == null || walletWithdrawBoList.isEmpty()) {
            return R.fail("提款请求列表不能为空");
        }

        int successCount = 0;
        int totalCount = walletWithdrawBoList.size();

        log.info("开始批量提款，共{}笔交易", totalCount);

        for (WalletWithdrawBo withdrawBo : walletWithdrawBoList) {
            try {
                // 获取资金钱包私钥
                String privateKey = getFundWalletPrivateKey(withdrawBo.getChainType());
                if (privateKey == null) {
                    log.error("{}链资金钱包未配置", withdrawBo.getChainType().getDisplayName());
                    continue;
                }

                // 构建转账请求
                TransferRequest request = TransferRequest.builder()
                    .privateKey(privateKey)
                    .toAddress(withdrawBo.getToAddress())
                    .amount(withdrawBo.getAmount().toString())
                    .tokenSymbol(withdrawBo.getTokenSymbol())
                    .chainName(withdrawBo.getChainType().getCode())
                    .enableFeeWallet(true)
                    .businessType("withdraw")
                    .stpValue(StpUtil.getTokenValue())
                    .waitForConfirmation(false) // 批量时不等待确认，提高处理速度
                    .memo(withdrawBo.getMemo() != null ? withdrawBo.getMemo() : "提款操作")
                    .requestId(withdrawBo.getRequestId())
                    .build();

                // 设置为异步模式
                request.setSyncMode(false);

                // 执行转账
                UnifiedTransferResult transferResult = unifiedTransferService.transfer(request);

                if (transferResult.isSuccess()) {
                    successCount++;
                    log.info("提款成功: {} {} {} -> {}, txHash: {}",
                        withdrawBo.getChainType().getDisplayName(),
                        withdrawBo.getAmount(),
                        withdrawBo.getTokenSymbol(),
                        withdrawBo.getToAddress(),
                        transferResult.getOrderId());
                } else {
                    log.error("提款失败: {} {} {} -> {}, error: {}",
                        withdrawBo.getChainType().getDisplayName(),
                        withdrawBo.getAmount(),
                        withdrawBo.getTokenSymbol(),
                        withdrawBo.getToAddress(),
                        transferResult.getErrorMessage());
                }

            } catch (Exception e) {
                log.error("提款处理异常: {} {} {} -> {}, error: {}",
                    withdrawBo.getChainType().getDisplayName(),
                    withdrawBo.getAmount(),
                    withdrawBo.getTokenSymbol(),
                    withdrawBo.getToAddress(),
                    e.getMessage());
            }
        }

        String resultMessage = String.format("批量提款完成，成功: %d/%d，详细结果请查看转账记录", successCount, totalCount);
        log.info(resultMessage);

        return R.ok(resultMessage);
    }

    /**
     * 根据链类型获取资金钱包私钥
     *
     * @param chainType 链类型
     * @return 解密后的私钥，如果未配置则返回null
     */
    private String getFundWalletPrivateKey(ChainType chainType) {
        String encryptedPrivateKey = switch (chainType) {
            case TRON -> tronConfigFacade.getFeeWalletPrivateKey();
            case BSC -> bscConfigFacade.getFeeWalletPrivateKey();
            case ARB -> arbConfigFacade.getFeeWalletPrivateKey();
            case BASE -> baseConfigFacade.getFeeWalletPrivateKey();
            case AVAX -> null; // TODO: 添加AVAX配置门面后更新
            case SOLANA -> solanaConfigFacade.getFeeWalletPrivateKey();
        };

        return encryptedPrivateKey != null ?
            WalletPrivateKeyUtil.getDecryptedPrivateKey(encryptedPrivateKey) : null;
    }


}
